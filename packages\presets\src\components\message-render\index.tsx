import React, { forwardRef, useImperativeHandle, useState } from "react";

import {
  MessageRender as CoreMessageRender,
  IMessagePackage,
  Message,
  MessageReceiver,
  MessageReceiverEvent,
  MessageStatus,
} from "@cscs-agent/core";

import { Transmitter } from "./transmitter";

const MessageRender = forwardRef<
  { runReq: () => void },
  {
    message?: Message;
    requestOptions: {
      url: string;
      method?: string;
      headers?: Record<string, string>;
      body?: Record<string, string>;
      params?: Record<string, string>;
    };
  }
>(function Render(props, ref) {
  const { message, requestOptions } = props;
  const [loading, setLoading] = useState(false);
  const [$message, set$Message] = React.useState<Message | null>(message ?? null);

  useImperativeHandle(ref, () => ({
    runReq: () => {
      if (loading) return;
      requestMessage();
    },
    cancelAndRunReq: () => {}
  }));

  const requestMessage = () => {
    const { url, method, headers, body, params } = requestOptions;

    const $transmitter = new Transmitter(url, {
      headers,
      method,
    });

    setLoading(true);

    // 发送消息
    $transmitter
      .send(params, body)
      .then((response) => {
        const messageReceiver = new MessageReceiver(response.data);

        messageReceiver.receive();

        // 监听接收到消息头事件
        messageReceiver.on(MessageReceiverEvent.HEADER_RECEIVED, () => {});

        // 监听消息完成事件
        messageReceiver.on(MessageReceiverEvent.MESSAGE_FINISHED, () => {
          set$Message((prev) => {
            if (prev) {
              return {
                ...prev,
                status: MessageStatus.Finished,
              } as Message;
            }
            return prev;
          });
        });

        // 监听正在接收消息包事件
        messageReceiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, (packageObj: IMessagePackage) => {
          // 更新对应的消息
          set$Message((message) => {
            if (message) {
              return updateLoadingMessagePackage(message, packageObj);
            } else {
              return message;
            }
          });
        });

        // 监听消息包接收完成事件
        messageReceiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, () => {
          // 如果没有 loading 状态的
        });

        // 监听消息包异常
        messageReceiver.on(MessageReceiverEvent.ERROR, () => {
          // 如果没有 loading 状态的
        });

        messageReceiver.on(MessageReceiverEvent.DONE, () => {
          // TODO 中断状态判断
        });
      })
      .catch(() => {
        // TODO 取消
        // cancelStaleChatRequest($transmitter);
      });
  };

  return <div className="message-render">{$message !== undefined && <CoreMessageRender data={$message} />}</div>;
});

function updateLoadingMessagePackage(message: Message, packageObj: IMessagePackage) {
  if (message && message.status === MessageStatus.Loading) {
    // 如果最后一条package的id等于当前消息的id且状态为loading，则更新package
    if (message.content.length > 0) {
      const lastPackage = message.content[message.content.length - 1];
      if (lastPackage.package_id === packageObj.package_id) {
        message.content.pop();
      }
    }
    // 插入最新的package
    message.content.push(packageObj);
  }
  return message;
}

export default MessageRender;

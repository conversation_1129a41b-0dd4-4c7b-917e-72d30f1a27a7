/**
 * Tests for ProjectUpgradeAnalyzer refactored implementation
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

import type { FileConfig } from "../../types.js";
import { ProjectUpgradeAnalyzer } from "../project-upgrade-analyzer.js";

// Mock the FileSystemManager
vi.mock("../filesystem-manager.js", () => ({
  FileSystemManager: vi.fn().mockImplementation(() => ({
    fileExists: vi.fn(),
    readFile: vi.fn(),
    writeFile: vi.fn(),
    updateFile: vi.fn(),
  })),
}));

// Mock the Logger
vi.mock("../logger.js", () => ({
  Logger: {
    info: vi.fn(),
    warning: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock @openai/agents
vi.mock("@openai/agents", () => ({
  Agent: vi.fn().mockImplementation(() => ({})),
  run: vi.fn(),
}));

// Mock fs
vi.mock("fs", () => ({
  existsSync: vi.fn(),
}));

describe("ProjectUpgradeAnalyzer", () => {
  let analyzer: ProjectUpgradeAnalyzer;
  let mockFsManager: any;
  let mockExistsSync: any;
  let mockRun: any;

  const samplePackageJson = JSON.stringify({
    name: "test-project",
    version: "1.0.0",
    dependencies: {
      "@cscs-agent/core": "^1.0.0",
      react: "^18.0.0",
    },
    devDependencies: {
      typescript: "^5.0.0",
    },
  });

  const sampleMainTsx = `import React from 'react';
import { createRoot } from 'react-dom/client';
import { AgentChat } from '@cscs-agent/core';

const root = createRoot(document.getElementById('root')!);
root.render(<AgentChat />);`;

  const sampleAgentConfig = `import type { AgentChatConfig } from "@cscs-agent/core";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Test Agent",
      code: "test-agent",
      description: "A test agent",
    },
  ],
};`;

  beforeEach(async () => {
    // Import the mocked modules
    const fs = await import("fs");
    const { run } = await import("@openai/agents");
    mockExistsSync = fs.existsSync as any;
    mockRun = run as any;

    analyzer = new ProjectUpgradeAnalyzer();

    // Create mock methods
    mockFsManager = {
      fileExists: vi.fn(),
      readFile: vi.fn(),
      writeFile: vi.fn(),
      updateFile: vi.fn(),
    };

    // Replace the fsManager instance
    (analyzer as any).fsManager = mockFsManager;

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("analyzeFiles", () => {
    it("should analyze multiple files in batch", async () => {
      const fileConfigs: FileConfig[] = [
        {
          filePath: "package.json",
          analysisContext: "Test package.json analysis",
          required: true,
          priority: 10,
        },
        {
          filePath: "src/main.tsx",
          analysisContext: "Test main.tsx analysis",
          required: true,
          priority: 9,
        },
      ];

      // Mock file existence and content
      mockExistsSync.mockImplementation((path: string) => {
        return path.includes("package.json") || path.includes("main.tsx");
      });

      mockFsManager.readFile.mockImplementation((path: string) => {
        if (path.includes("package.json")) return Promise.resolve(samplePackageJson);
        if (path.includes("main.tsx")) return Promise.resolve(sampleMainTsx);
        return Promise.resolve("");
      });

      // Mock LLM response
      mockRun.mockResolvedValue({
        finalOutput: "Test analysis result",
      });

      const results = await analyzer.analyzeFiles("/test/project", fileConfigs);

      expect(results).toHaveProperty("package");
      expect(results).toHaveProperty("main");
      expect(results.package.exists).toBe(true);
      expect(results.main.exists).toBe(true);
      expect(results.package.dependencies).toEqual({
        "@cscs-agent/core": "^1.0.0",
        react: "^18.0.0",
      });
    });

    it("should handle non-existent files", async () => {
      const fileConfigs: FileConfig[] = [
        {
          filePath: "src/agent-config.tsx",
          analysisContext: "Test agent config analysis",
          required: false,
          priority: 8,
        },
      ];

      // Mock file not existing
      mockExistsSync.mockReturnValue(false);

      const results = await analyzer.analyzeFiles("/test/project", fileConfigs);

      expect(Object.keys(results)).toHaveLength(0);
    });

    it("should use alternative paths when primary path doesn't exist", async () => {
      const fileConfigs: FileConfig[] = [
        {
          filePath: "src/main.tsx",
          alternativePaths: ["src/main.ts", "src/index.tsx"],
          analysisContext: "Test main file analysis",
          required: true,
          priority: 9,
        },
      ];

      // Mock primary path not existing, but alternative path existing
      mockExistsSync.mockImplementation((path: string) => {
        return path.includes("index.tsx");
      });

      mockFsManager.readFile.mockResolvedValue(sampleMainTsx);
      mockRun.mockResolvedValue({ finalOutput: "Test analysis" });

      const results = await analyzer.analyzeFiles("/test/project", fileConfigs);

      const mainResult = Object.values(results)[0];
      expect(mainResult.exists).toBe(true);
      expect(mainResult.filePath).toContain("index.tsx");
    });
  });

  describe("analyzeProject", () => {
    it("should analyze complete project with dynamic file discovery", async () => {
      // Mock file existence for core files
      mockExistsSync.mockImplementation((path: string) => {
        return (
          path.includes("package.json") ||
          path.includes("main.tsx") ||
          path.includes("vite.config.ts") ||
          path.includes("tsconfig.json")
        );
      });

      // Mock file content
      mockFsManager.readFile.mockImplementation((path: string) => {
        if (path.includes("package.json")) return Promise.resolve(samplePackageJson);
        if (path.includes("main.tsx")) return Promise.resolve(sampleMainTsx);
        if (path.includes("vite.config.ts")) return Promise.resolve("export default { plugins: [] }");
        if (path.includes("tsconfig.json")) return Promise.resolve('{"compilerOptions": {}}');
        return Promise.resolve("");
      });

      // Mock LLM responses
      mockRun.mockResolvedValue({
        finalOutput: "Detailed analysis result with categorization and recommendations",
      });

      const result = await analyzer.analyzeProject("/test/project");

      // Check dynamic structure
      expect(result.files).toBeDefined();
      expect(Object.keys(result.files).length).toBeGreaterThan(0);

      // Check that package.json and main.tsx are in the files collection
      const packageJsonFile = Object.values(result.files).find(
        (file) => file.filePath.endsWith("package.json") && file.exists,
      );
      const mainFile = Object.values(result.files).find(
        (file) =>
          ["main.tsx", "main.ts", "index.tsx", "index.ts", "App.tsx", "App.ts"].some((pattern) =>
            file.filePath.includes(pattern),
          ) && file.exists,
      );

      expect(packageJsonFile).toBeDefined();
      expect(mainFile).toBeDefined();

      // Check project summary
      expect(result.projectSummary).toBeDefined();
      expect(result.isValidAgentProject).toBe(true);

      // Check additional files
      expect(result.additionalFiles.length).toBeGreaterThan(0);
    });
  });

  describe("analyzeProject integration", () => {
    it("should use the new generic method internally", async () => {
      // Mock all files existing
      mockExistsSync.mockReturnValue(true);
      mockFsManager.readFile.mockImplementation((path: string) => {
        if (path.includes("package.json")) return Promise.resolve(samplePackageJson);
        if (path.includes("main.tsx")) return Promise.resolve(sampleMainTsx);
        if (path.includes("agent-config.tsx")) return Promise.resolve(sampleAgentConfig);
        return Promise.resolve("");
      });

      mockRun.mockResolvedValue({ finalOutput: "Analysis result" });

      const result = await analyzer.analyzeProject("/test/project");

      // Check that all expected files are in the files collection
      const packageJsonFile = Object.values(result.files).find(
        (file) => file.filePath.endsWith("package.json") && file.exists,
      );
      const mainFile = Object.values(result.files).find(
        (file) =>
          ["main.tsx", "main.ts", "index.tsx", "index.ts", "App.tsx", "App.ts"].some((pattern) =>
            file.filePath.includes(pattern),
          ) && file.exists,
      );
      const agentConfigFile = Object.values(result.files).find(
        (file) =>
          ["agent-config.tsx", "agent-config.ts", "agent.tsx", "agent.ts"].some((pattern) =>
            file.filePath.includes(pattern),
          ) && file.exists,
      );

      expect(packageJsonFile).toBeDefined();
      expect(mainFile).toBeDefined();
      expect(agentConfigFile).toBeDefined();
      expect(result.isValidAgentProject).toBe(true);
    });
  });
});

#!/usr/bin/env node

/**
 * CSCS Agent CLI - Main entry point
 */

import "dotenv/config";

import { readFileSync } from "fs";
import { dirname, join } from "path";
import { fileURLToPath } from "url";

import { Command } from "commander";
import { vice } from "gradient-string";
import { OpenAI } from "openai";

import { setDefaultOpenAIClient, setOpenAIAPI } from "@openai/agents";

import { CreateCommand } from "./commands/create.js";
import { GenerateCommand } from "./commands/generate.js";
import { UpgradeCommand } from "./commands/upgrade.js";
import { logo } from "./logo.js";
import { Logger } from "./utils/logger.js";

// set custom OpenAI client only if API key is provided
if (process.env.OPENAI_API_KEY) {
  const customClient = new OpenAI({ baseURL: process.env.OPENAI_BASE_URL, apiKey: process.env.OPENAI_API_KEY });
  setDefaultOpenAIClient(customClient);
  setOpenAIAPI("chat_completions");
}

// Get package.json for version info
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packageJsonPath = join(__dirname, "..", "package.json");
const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"));

const program = new Command();

console.log(vice.multiline(logo));

// Configure main program
program
  .name("@cscs-agent/cli")
  .description("CLI tool for creating Agent projects from templates")
  .version(packageJson.version, "-v, --version", "display version number");

// Create command
program
  .command("create [project-name]")
  .description("Create a new Agent project")
  .option("-t, --template <template>", "Template to use")
  .option("-d, --directory <directory>", "Target directory (default: project name)")
  .option("--skip-install", "Skip dependency installation")
  .option("--package-manager <manager>", "Package manager to use (npm, yarn, pnpm)")
  .option("-i, --interactive", "Run in interactive mode")
  .action(async (projectName, options) => {
    const createCommand = new CreateCommand();
    await createCommand.execute(projectName, options);
  });

// Generate command
program
  .command("generate")
  .alias("g")
  .description("Generate a basic widget component")
  .option("-n, --name <name>", "Widget name (PascalCase)")
  .option("-p, --target-path <path>", "Target project path", process.cwd())
  .option("-d, --description <description>", "Widget description")
  .option("--props <props>", "Widget props as JSON string")
  .option("-i, --interactive", "Run in interactive mode")
  .action(async (options) => {
    const generateCommand = new GenerateCommand();
    await generateCommand.execute(options);
  });

// Upgrade command
program
  .command("upgrade")
  .description("[Experimental] Upgrade project using AI")
  .option("-p, --target-path <path>", "Target project path", process.cwd())
  .option("-i, --interactive", "Run in interactive mode")
  .option("--skip-backup", "Skip creating backup files")
  .option("--dry-run", "Preview changes without applying them")
  .option("--force", "Force upgrade even if there are warnings")
  .action(async (options) => {
    const upgradeCommand = new UpgradeCommand();
    await upgradeCommand.execute(options);
  });

// List templates command
program
  .command("list")
  .alias("ls")
  .description("List available templates")
  .action(() => {
    const createCommand = new CreateCommand();
    createCommand.listTemplates();
  });

// Templates command (alias for list)
program
  .command("templates")
  .description("List available templates")
  .action(() => {
    const createCommand = new CreateCommand();
    createCommand.listTemplates();
  });

// Info command
program
  .command("info")
  .description("Show CLI information")
  .action(() => {
    Logger.title("CSCS Agent CLI Information");

    Logger.table([
      { key: "Version", value: packageJson.version },
      { key: "Description", value: packageJson.description },
      { key: "Node Version", value: process.version },
      { key: "Platform", value: process.platform },
      { key: "Architecture", value: process.arch },
    ]);

    Logger.newLine();
    Logger.subtitle("Available Commands:");
    Logger.list([
      "create [project-name] - Create a new project",
      "generate - Generate a basic widget component",
      "list - List available templates",
      "info - Show CLI information",
      "help - Show help information",
    ]);

    Logger.newLine();
  });

// Global error handler
process.on("uncaughtException", (error) => {
  Logger.error(`Uncaught exception: ${error.message}`);
  process.exit(1);
});

process.on("unhandledRejection", (reason) => {
  Logger.error(`Unhandled rejection: ${reason}`);
  process.exit(1);
});

// Custom help
program.on("--help", () => {
  console.log("");
  console.log("Examples:");
  console.log("  $ npx @cscs-agent/cli create my-app");
  console.log("  $ npx @cscs-agent/cli create my-app --template basic");
  console.log("  $ npx @cscs-agent/cli create --interactive");
  console.log("  $ npx @cscs-agent/cli list");
  console.log("");
});

// Parse arguments and execute
program.parse();

// If no command provided, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}

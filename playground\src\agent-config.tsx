import { But<PERSON> } from "@@/antd/es";
import { SearchOutlined, SortAscendingOutlined } from "@ant-design/icons";
import { dynamicPageConfigFactory, dynamicPageQaAgentConfig, nl2sqlAgentConfig } from "@cscs-agent/agents";
import { Role, type AgentChatConfig } from "@cscs-agent/core";
import { Copy, PromptTemplate, Rating, ThoughtChain } from "@cscs-agent/presets";

import InsertTag from "./widgets/insert-tag";
import InsertText from "./widgets/insert-text";
import FormDemo from "./widgets/form";

const dynamicPageAgentConfig = dynamicPageConfigFactory({
  saveApiUrl: () => {
    return `/gateway/cluster/page/system/dynamic/page/manage/savePageForAi`;
  },
  previewUrl: (id: string) => `http://172.17.6.116:8002/dynamic_page/agent_preview/${id}`,
});

export const config: AgentChatConfig = {
  agents: [
    dynamicPageAgentConfig,
    dynamicPageQaAgentConfig,
    nl2sqlAgentConfig,
    {
      name: "测试智能体",
      code: "default",
      message: {
        blocks: {
          widgets: [
            {
              code: "@DynamicPage/PreviewButton",
              component: () => {
                return <Button>PreviewWidget</Button>;
              },
            },
            {
              code: "@BuildIn/ThoughtChain",
              component: ThoughtChain,
            },
            {
              code: "@Test/FormDemo",
              component: FormDemo,
            },
          ],
        },
        slots: {
          footer: {
            widgets: [
              {
                code: "Copy",
                component: Copy,
                role: Role.AI,
              },
              {
                code: "Rating",
                component: Rating,
                role: Role.AI,
              },
            ],
          },
        },
      },
      prompts: [
        {
          icon: <SearchOutlined />,
          title: "字段查询字段查询字段查询",
          description: "根据字段查询列表",
          prompt: "请查询{{[企业名称]}}获取企业信息",
        },
        {
          icon: <SortAscendingOutlined />,
          title: "排序",
          description: "配置列表排序",
          prompt: "请对列表进行排序",
        },
      ],
      commands: [
        {
          name: "",
          action: () => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          headerPanel: {
            widgets: [
              {
                code: "PromptTemplate",
                component: PromptTemplate,
              },
            ],
          },
          header: {
            widgets: [
              {
                code: "InsertText",
                component: InsertText,
              },
              {
                code: "InsertTag",
                component: InsertTag,
              },
            ],
          },
          footer: {
            widgets: [],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [],
        },
      },
      request: {
        chat: {
          url: "/chat/test",
          headers: {},
          method: "get",
        },
      },
    },
  ],
};
